<template>
  <div class="pro-result" id="ProResult">
    <div class="filter">
      <CustomSelect
        :options="rtmList"
        v-model="rtm"
        class="select"
        @change="handleRtmChange"
      >
        <template slot="prefix">RTM:</template>
      </CustomSelect>
      <SearchInput
        class="select-search"
        :options="searchData"
        placeholder="Search Sold-to Name / ID"
        :searchValue="search_key"
        @change:searchValue="changeSearchValue"
        placement="bottom"
        sold_to_name_key="short_name"
      />

      <ColumnsVisible
        @change="handleColumnsVisibleChange"
        ref="columnsVisible"
      />
    </div>
    <Table
      :columns="columns"
      :data="resultData"
      :loading="loading"
      :border="true"
      :cell-class-name="resultCellClassName"
      :header-cell-class-name="resultHeaderCellClassName"
      row-key="id"
      ref="table"
      :row-class-name="resultRowClassName"
      @expand-change="expandChange"
      v-tool-tip.delegate="{ targetClassName: 'toolBox' }"
      class="self-table"
    >
      <template #rtm="{ scope }">
        <div class="header">
          <!-- <span
            v-if="scope.row.rtm !== 'China Total'"
            @click.stop="handleExpand(scope.row)"
            :class="[
              'expand-btn',
              {
                'is-leaf': scope.row.sub_rtm !== 'All',
              },
            ]"
          >
            <i
              :class="
                scope.row.expand
                  ? 'el-icon-remove-outline'
                  : 'el-icon-circle-plus-outline'
              "
            ></i>
          </span> -->
          <span class="icon" v-if="scope.row.rtm === 'Grand Total'">
            <svg-icon data_iconName="event-InvestAll" class="total-icon" />
          </span>
          <span>{{
            scope.row.rtm === "Strategic Total"
              ? "Program Total"
              : scope.row.rtm
          }}</span>
        </div>
      </template>
      <template #subRtm="{ scope }">
        <div class="header">
          <span
            @click.stop="handleExpand(scope.row)"
            :class="[
              'expand-btn',
              {
                'is-leaf':
                  scope.row.sold_to_name !== 'All' ||
                  scope.row.sub_rtm === 'All',
              },
            ]"
          >
            <i
              :class="
                scope.row.expand
                  ? 'el-icon-remove-outline'
                  : 'el-icon-circle-plus-outline'
              "
            ></i>
          </span>
          <span>{{ scope.row.sub_rtm }}</span>
        </div>
      </template>
      <template #soldTo="{ item, row }">
        <div class="toolBox">
          {{ row[item.prop] }}
        </div>
      </template>
      <template #capReward="{ scope }">
        <span
          class="cap-reward"
          :class="[
            rewardColor(scope.row.original_allocation_mix_woi_cap_vs_reward),
          ]"
        >
          {{ scope.row.allocation_mix_woi_cap_vs_reward }}
        </span>
      </template>

      <template #commonHeader="{ item }">
        <div class="header-box">
          <div class="label">{{ item.label }}</div>
          <div class="sub-arr" v-if="item.childrenArr">
            <template v-for="subItem in item.childrenArr">
              <div :key="subItem.prop" class="sub-item">
                {{ subItem.label }}
              </div>
            </template>
          </div>
          <div class="sub-label" v-else>{{ item.subLabel }}ss</div>
        </div>
      </template>
      <template #tipsHeader="{ item }">
        <div class="supply-unit">
          {{ item.label }}
          <el-tooltip
            effect="dark expert-custom-tooltip unit-tips"
            placement="top-end"
          >
            <div slot="content" v-html="item.tips"></div>
            <div class="tips">
              <svg-icon data_iconName="npi-icon_note"></svg-icon>
            </div>
          </el-tooltip>
        </div>
      </template>
    </Table>
  </div>
</template>

<script>
import Table from "@/pages/AntiFraud/components/Table.vue";
import CustomSelect from "@/components/CustomSelect.vue";
import ColumnsVisible from "./ColumnsVisible.vue";
import SearchInput from "../../components/SearchInput.vue";
// import stacky from "@/utils/table-sticky";

import { resultColumns, resultCommonColumns } from "../../constant";
import { formatResultData, isNullOrUndefinedOrEmptyString } from "../../format";
import { isEqual } from "lodash-es";

export default {
  name: "ExpertResult",
  // mixins: [stacky],
  components: {
    Table,
    CustomSelect,
    ColumnsVisible,
    SearchInput,
  },
  props: {
    tab: {
      type: String,
      default: "Pro",
    },
  },
  data() {
    return {
      columns: [...resultCommonColumns, ...resultColumns],

      search_key: "",
      resultData: [],

      rtm: "All",
      sold_to_id: "",
      expandParent: null,
      openSoldToList: [],
      parent: "self-table",
    };
  },
  computed: {
    viewData() {
      return this.$store.state.npiAllocation[this.tab].viewData;
    },
    tabValue() {
      return this.$store.state.npiAllocation.tabValue;
    },
    loading() {
      return this.$store.state.npiAllocation[this.tabValue].loading;
    },
    sold_to_menu() {
      return this.$store.state.npiAllocation.sold_to_menu;
    },
    rtmList() {
      const arr = ["All"];
      this.sold_to_menu.forEach((item) => {
        if (!arr.includes(item.rtm)) {
          arr.push(item.rtm);
        }
      });
      return arr.map((item) => ({
        label: item,
        value: item,
      }));
    },
    searchData() {
      if (this.rtm === "All") {
        return this.sold_to_menu;
      }
      return this.sold_to_menu.filter((item) => item.rtm === this.rtm);
    },

    searchParams() {
      return this.$store.state.npiAllocation[this.tabValue].searchParams;
    },
    selectColumns() {
      return this.columns.map((item) => item.value);
    },
  },
  watch: {
    viewData: {
      handler(val) {
        this.resultData = formatResultData(val);
        if (this.expandParent) {
          this.$refs.table?.$refs.table.toggleRowExpansion(
            this.expandParent,
            false
          );
        }
        console.log(
          "result",
          this.resultData,
          this.searchParams,
          this.expandParent
        );
        if (this.resultData.length && this.searchParams.sold_to_id) {
          const findRow = (data) => {
            for (const item of data) {
              if (item.sold_to_id === this.searchParams.sold_to_id) {
                return {
                  current: item,
                  parent: null,
                };
              }
              if (item.children?.length) {
                for (const child of item.children) {
                  if (child.sold_to_id === this.searchParams.sold_to_id) {
                    return {
                      current: child,
                      parent: item,
                    };
                  }
                }
              }
            }
            return null;
          };
          const result = findRow(this.resultData);
          if (result) {
            console.log("result", result);
            const { parent, current } = result;
            if (current) {
              this.expandParent = parent;
              setTimeout(() => {
                this.$refs.table?.$refs.table.toggleRowExpansion(parent, true);
              }, 0);
            }
          }
        } else {
          this.$nextTick(() => {
            (this.resultData || []).forEach((item) => {
              if (Array.isArray(item.children) && item.children.length) {
                this.$refs.table?.$refs.table.toggleRowExpansion(item, true);
              }
            });
          });
        }
      },
      deep: true,
    },
    searchParams: {
      handler(val, old) {
        if (val.tier === this.tab) {
          console.log("load", this.viewData, val);
          if (this.viewData.length > 0 && isEqual(val, old)) {
            return;
          }
          this.$store.dispatch("fetchView", val);
        }
      },
      deep: true,
      immediate: true,
    },
  },

  mounted() {
    // window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    // window.removeEventListener("resize", this.handleResize);
  },

  methods: {
    resultCellClassName({ column }) {
      let className = "";
      if (
        [
          "pos_authorized_cnt",
          "pos_suspension_cnt",
          "allocation_mix",
          "allocation_mix_woi_cap",
          "allocation_mix_woi_cap_vs_reward",
          "allocation_qty",
          "avg_allocation_per_pos",
          "npi_weekly_runrate",
          "rtm",
          "sub_rtm",
        ].includes(column.property)
      ) {
        className += "no-border";
      }
      if (column.property === "pos_on_suspension") {
        className += " font-yellow";
      }
      if (["allocation_qty", "woi"].includes(column.property)) {
        className += " bg-blue";
      }
      return className;
    },
    resultHeaderCellClassName({ column }) {
      let className = "";
      if (
        [
          "pos_authorized_cnt",
          "pos_authorized_cnt_h",
          "pos_suspension_cnt_h",
          "pos_suspension_cnt",
          "allocation_mix_h",
          "allocation_mix",
          "allocation_mix_woi_cap_menu",
          "allocation_mix_woi_cap",
          "allocation_mix_woi_cap_vs_reward",
          "allocation_qty",
          "allocation_qty_h",
          "avg_allocation_per_pos_h",
          "avg_allocation_per_pos",
          "npi_weekly_runrate_h",
          "npi_weekly_runrate",
          "rtm",
          "sub_rtm",
        ].includes(column.property)
      ) {
        className += "no-border";
      }
      if (["rtm", "sub_rtm", "sold_to_name_abbre"].includes(column.property)) {
        className += " new-header";
      }
      //   if (
      //     [
      //       "allocation_mix_woi_cap",
      //       "allocation_mix_woi_cap_vs_reward",
      //       "pos_authorized_minus_suspension",
      //       "authorized_pos_suspension_pct",
      //     ].includes(column.property)
      //   ) {
      //     className += " hidden";
      //   }

      return className;
    },
    resultRowClassName({ row }) {
      if (
        [
          "Grand Total",
          "Program",
          "Offline Total",
          "Online Total",
          "Vertical",
        ].includes(row.rtm)
      ) {
        return "weighted-row";
      }
    },
    handleExpand(row) {
      // if (row.sub_rtm !== "All") {
      //   if (
      //     !row.expand &&
      //     !this.columns.some((item) => item.prop === "sold_to_name_abbre")
      //   ) {
      //     this.openSoldToList.push(row.id);
      //     this.$refs.columnsVisible.addColumn(["sold_to_name_abbre"]);
      //   } else {
      //     if (this.openSoldToList.includes(row.id)) {
      //       this.openSoldToList = this.openSoldToList.filter(
      //         (item) => item !== row.id
      //       );
      //     }
      //     if (this.openSoldToList.length === 0) {
      //       this.$refs.columnsVisible.removeColumn(["sold_to_name_abbre"]);
      //     }
      //   }
      // }

      this.$refs.table?.$refs.table.toggleRowExpansion(row);
    },
    expandChange(row, expand) {
      row.expand = expand;
    },
    handleColumnsVisibleChange(val) {
      const cols = resultColumns.filter((item) => {
        if (val.includes(item.prop)) {
          return item;
        }
      });
      this.columns = [...resultCommonColumns, ...cols];
      this.$nextTick(() => {
        this.$refs.table.$refs.table.doLayout();
      });
    },
    changeSearchValue(value) {
      if (value) {
        // const item = this.searchData.find((item) => item.sold_to_id === value);
        const item = value.split("-");
        this.search_key = value;
        this.expandParent = null;
        this.$store.commit("SET_NPI_SEARCH_PARAMS", {
          ...this.searchParams,
          sub_rtm: item[2],
          sold_to_id: item[0],
          rtm: this.rtm === "All" ? item[1] : this.rtm,
        });
      } else {
        this.search_key = "";
        this.$store.commit("SET_NPI_SEARCH_PARAMS", {
          ...this.searchParams,
          rtm: this.rtm,
          sub_rtm: "",
          sold_to_id: "",
        });
      }
    },
    handleRtmChange(val) {
      this.search_key = "";
      this.$store.commit("SET_NPI_SEARCH_PARAMS", {
        ...this.searchParams,
        rtm: val,
        sub_rtm: "",
        sold_to_id: "",
        tier: this.tabValue,
      });
    },
    rewardColor(val) {
      if (isNullOrUndefinedOrEmptyString(val)) return "";
      return val < 0 ? "font-red" : "font-green";
    },
  },
};
</script>
<style lang="scss" scoped>
.pro-result {
  padding-top: 24px;
  .filter {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 16px;
    .select {
      width: 240px;
    }
    .select-search {
      width: 320px;
      box-sizing: border-box;
    }
    ::v-deep .search-container.el-select .el-input__inner {
      height: 48px;
    }
  }
  ::v-deep .el-table {
    min-height: 300px;
    .el-table__expand-icon {
      display: none !important;
    }
    border-radius: 16px;
    .cell {
      padding-left: 16px;
      padding-right: 16px;
      font-size: 16px;
    }
    th.el-table__cell {
      background-color: #fafafa;
      padding: 0;
      &.new-header {
        background-color: #fff;

        .cell {
          padding: 0;
          .header-box {
            display: flex;
            align-items: center;
            flex-direction: column;

            .label {
              padding: 10px 16px;
              background-color: #fafafa;
              border-bottom: 1px solid #ebeef5;
              width: 100%;
              box-sizing: border-box;
            }
            .sub-label {
              padding: 10px 16px;
              visibility: hidden;
            }
          }
        }
      }
      > .cell {
        white-space: pre-wrap;
        font-size: 16px;
        font-weight: normal;
        color: #6e6e73;
        padding: 10px 16px;
      }
      &.hidden {
        display: none;
      }
      &.no-border {
        border-right: none;
      }
    }

    .hover-row td {
      background-color: transparent;
    }
    td.el-table__cell {
      &.no-border {
        border-right: none;
      }
      &.bg-red {
        background-color: rgba(246, 63, 84, 0.08);
      }
      &.bg-blue {
        background-color: rgba(0, 113, 227, 0.08);
      }
      &.font-yellow {
        color: #ff9500;
      }

      .font-red {
        color: #f63f54;
      }
      .font-green {
        color: #34c759;
      }
      .cell {
        display: flex;
      }
    }
    thead tr:not(:first-child) {
      th.el-table__cell {
        background-color: transparent;
      }
    }
    .weighted-row {
      .cell {
        font-weight: $fontMedium;
        color: #1c1c1e;
      }
    }

    .el-table__row--level-0 {
      font-weight: $fontMedium;
      color: #1c1c1e;
    }
  }
  ::v-deep .table-wrap .loading {
    height: calc(100% - 41px);
    top: 41px;
    // background: linear-gradient(#fff, rgba(#fff, 0.44), rgba(#fff, 0.28));
    background-color: rgba(255, 255, 255, 0.08);

    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    color: #1c1c1e;
  }

  ::v-deep
    .el-table--enable-row-hover
    .el-table__body
    tr:hover
    > td.el-table__cell {
    background-color: transparent;
  }
  .header {
    display: flex;
    align-items: center;
  }
  .expand-btn {
    cursor: pointer;
    line-height: 1;
    align-items: center;
    display: flex;
    &.is-leaf {
      cursor: default;
      pointer-events: none;
      i {
        visibility: hidden;
      }
    }
    i {
      width: 16px;
      font-size: 16px;
      cursor: pointer;
      margin-right: 4px;
      font-weight: 600;
    }
  }
  .icon {
    @include flex(center);
    background: #000;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    margin-right: 5px;
  }

  .total-icon {
    font-size: 9px;
    color: #fff;
  }
  .toolBox {
    cursor: default;
    position: relative;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    overflow: hidden;
  }
  .supply-unit {
    display: flex;
    align-items: center;
    .tips {
      cursor: pointer;
      display: flex;
      align-items: center;
      color: #aeaeb2;
      &.edit-tips {
        color: #0071e3;
      }
    }
    .svg-icon {
      margin-left: 4px;
      width: 22px;
      height: 22px;
      font-size: 22px;
    }
  }
  .el-table__header-wrapper {
    position: sticky;
    top: 0;
    z-index: 2;
    background-color: white; /* 或者与你 header 一致的颜色 */
  }
}
</style>
