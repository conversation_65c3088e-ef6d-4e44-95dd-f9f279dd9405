import {
  FAST,
  CPF,
  MONO,
  MULTI,
  CARRIER,
  ENT,
  EDU,
  CHANNEL_ONLINE,
  ROLE_SALES_OPS,
} from "@/constant/fast";
import { userService } from "@/services/UserService";

export const npiSupplyAllocationTabs = [
  {
    svg: "iPhone",
    value: "1",
    label: "iPhone",
  },
  {
    svg: "iPad",
    value: "2",
    disabled: true,
    label: "iPad",
  },
  {
    svg: "i-Mac",
    value: "3",
    disabled: true,
    label: "mac",
  },
  {
    svg: "i-Watch",
    value: "4",
    disabled: true,
    label: "Watch",
  },
  {
    svg: "npi-Others",
    value: "5",
    disabled: true,
    label: "Others",
  },
];
export const SupplyPeriod = "FY25Q4 W11-W13";

export const CHIAN_TOTAL = "China_Total";

const posRole = () => {
  const list = [
    CPF,
    MONO,
    MULTI,
    CARRIER,
    CHANNEL_ONLINE,
    EDU,
    ENT,
    ROLE_SALES_OPS,
  ];
  // 如果有 Sales Support 返回 false

  return !list.some((role) => userService.hasRoleName(`${FAST} ${role}`));
};
export const npiSupplyAllocationCards = [
  {
    label1: "1st Batch",
    label2: "Allocation",
    supplyPeriod: SupplyPeriod,

    posLabel: "Download POS Allocation (by Mix)",
    posDownload: posRole,

    cdcLabel: "Download POS Allocation (by Mix)",
    cdcDownload: true,
    solver: () => userService.hasPlatform(`${FAST} ${CPF}`),
  },
  {
    label1: "1st Batch",
    label2: "Announcement Day Update",
    supplyPeriod: SupplyPeriod,

    posLabel: "Download POS Allocation",
    posDownload: posRole,
    cdcLabel: "Download POS Allocation",
    cdcDownload: true,
    solver: () => userService.hasPlatform(`${FAST} ${CPF}`),
  },
  {
    label1: "2nd Batch ",
    label2: "Allocation",
    supplyPeriod: SupplyPeriod,
    posLabel: "Download POS Allocation",
    posDownload: posRole,
    cdcLabel: "Download POS Allocation",
    cdcDownload: false,
    solver: () => userService.hasPlatform(`${FAST} ${CPF}`),
  },
];

export const calculatorColumns = [
  {
    prop: "supply_from",
    label: "",
    width: 168,
    definedSlot: "supplyFrom",
    // "class-name": "col-mask",
  },
  {
    prop: "ly_npi_mix",
    label: "LY NPI Mix",
    width: 188,
    headerSlot: "tipsHeader",
    tips: "Value calculated by summing up the LY NPI shipment plan <br/> from W11 to W13.",
    "class-name": "col-mask",
  },
  {
    prop: "bau_mix",
    label: "Q2+Q3 BAU Mix",
    width: 196,
    headerSlot: "tipsHeader",
    tips: "Value calculated by the Reward System.",
    "class-name": "col-mask",
  },
  {
    prop: "table_rtm_mix",
    label: "RTM Mix",
    width: 216,
    definedSlot: "rtmMix",
    "class-name": "col-mask-rtm-mix",
  },
  {
    prop: "supply_qty",
    label: "Supply Unit (K)",
    width: 216,
    headerSlot: "supplyUnitHeader",
    definedSlot: "supplyUnit",
    "class-name": "col-mask-supply-qty",
  },
  {
    prop: "ly_supply",
    label: "LY Supply (K)",
    width: 188,
    // definedSlot: "lySupply",
    "class-name": "col-mask",
  },

  {
    prop: "yoy",
    label: "YoY",
    width: 188,
    definedSlot: "yoy",
    "class-name": "col-mask",
  },
];

export const resultCommonColumns = [
  {
    prop: "rtm",
    label: "RTM",
    width: 168,
    fixed: "left",
    sLabel: "RTM",
    headerSlot: "commonHeader",
    definedSlot: "rtm",
    // children: [
    //   {
    //     prop: "rtm",
    //     label: "",
    //     width: 198,
    //     definedSlot: "rtm",
    //   },
    // ],
  },
  {
    prop: "sub_rtm",
    label: "Sub-Business Type",
    width: 202,
    fixed: "left",
    headerSlot: "commonHeader",
    definedSlot: "subRtm",
    // children: [
    //   {
    //     prop: "sub_rtm",
    //     label: "",
    //     width: 202,
    //     definedSlot: "subRtm",
    //   },
    // ],
  },
  {
    prop: "sold_to_name_abbre",
    label: "Sold-to",
    sLabel: "Sold-to",
    value: "sold_to_name_abbre",
    width: 187,
    fixed: "left",
    headerSlot: "commonHeader",
    definedSlot: "soldTo",
  },

  // children: [
  //   {
  //     prop: "sold_to_name_abbre",
  //     label: "",
  //     width: 187,
  //   },
  // ],
];
// export const resultColumns = [
//   {
//     prop: "pos_authorized_cnt",
//     label: "Total POS Authorized",
//     subLabel: "(Count)",
//     width: 224,
//     sLabel: "Total POS Authorized",
//     value: "pos_authorized_cnt",
//     headerSlot: "commonHeader",
//     // children: [
//     //   {
//     //     prop: "pos_authorized_cnt",
//     //     label: "(Count)",
//     //     width: 224,
//     //   },
//     // ],
//   },
//   {
//     prop: "pos_suspension_cnt",
//     label: "POS on Suspension",
//     subLabel: "(Count)",
//     sLabel: "POS on Suspension",
//     width: 208,
//     value: "pos_suspension_cnt",
//     headerSlot: "commonHeader",
//     // children: [
//     //   {
//     //     prop: "pos_suspension_cnt",
//     //     label: "(Count)",
//     //     width: 208,
//     //   },
//     // ],
//   },
//   {
//     prop: "supply_unit",
//     label: "POS w/ Allocation",
//     sLabel: "POS w/ Allocation",
//     value: "supply_unit",
//     disabled: true,
//     width: 192,
//     headerSlot: "commonHeader",
//     definedSlot: "common",
//     childrenArr: [
//       {
//         prop: "pos_authorized_minus_suspension",
//         label: "(Count)",
//         width: 110,
//       },
//       {
//         prop: "authorized_pos_suspension_pct",
//         label: "(%)",
//         width: 82,
//       },
//     ],
//   },
//   {
//     prop: "allocation_mix",
//     label: "Allocation Mix - Reward",
//     subLabel: "(%)",
//     sLabel: "Allocation Mix",
//     value: "allocation_mix",
//     width: 240,
//     headerSlot: "commonHeader",
//     // children: [
//     //   {
//     //     prop: "allocation_mix",
//     //     label: "(%)",
//     //     width: 240,
//     //   },
//     // ],
//   },
//   {
//     prop: "allocation_mix_woi_cap_menu",
//     label: "Allocation Mix (Cap by WOI)",
//     sLabel: "Allocation Mix (Cap by WOI)",
//     disabled: true,
//     value: "allocation_mix_woi_cap_menu",
//     width: 280,
//     headerSlot: "commonHeader",
//     definedSlot: "common",
//     childrenArr: [
//       {
//         prop: "allocation_mix_woi_cap",
//         label: "(%)",
//         width: 130,
//       },
//       {
//         prop: "allocation_mix_woi_cap_vs_reward",
//         label: "(Reward)",
//         width: 150,
//       },
//     ],
//   },
//   {
//     prop: "allocation_mix_ly",
//     label: "LY Allocation Mix",
//     subLabel: "(%)",
//     sLabel: "LY Allocation Mix",
//     value: "allocation_mix_ly",
//     width: 182,
//     headerSlot: "commonHeader",
//     // children: [
//     //   {
//     //     prop: "allocation_mix_ly",
//     //     label: "(%)",
//     //     width: 182,
//     //   },
//     // ],
//   },
//   {
//     prop: "allocation_qty",
//     label: "Allocation Unit",
//     subLabel: "(Unit, K)",
//     sLabel: "Allocation Unit",
//     value: "allocation_qty",
//     width: 168,
//     headerSlot: "commonHeader",
//     // children: [
//     //   {
//     //     prop: "allocation_qty",
//     //     label: "(Unit, K)",
//     //     width: 168,
//     //   },
//     // ],
//   },
//   {
//     prop: "allocation_yoy",
//     label: "Allocation Unit YoY",
//     subLabel: "(%)",
//     sLabel: "Allocation Unit YoY",
//     value: "allocation_yoy",
//     width: 200,
//     headerSlot: "commonHeader",
//     // children: [
//     //   {
//     //     prop: "allocation_yoy",
//     //     label: "(%)",
//     //     width: 200,
//     //   },
//     // ],
//   },
//   {
//     prop: "avg_allocation_per_pos",
//     label: "Avg Allocation per POS",
//     subLabel: "(Unit)",
//     sLabel: "Avg Allocation per POS",
//     value: "avg_allocation_per_pos",
//     width: 232,
//     headerSlot: "commonHeader",
//     // children: [
//     //   {
//     //     prop: "avg_allocation_per_pos",
//     //     label: "(Unit)",
//     //     width: 232,
//     //   },
//     // ],
//   },
//   {
//     prop: "npi_weekly_runrate",
//     label: "NPI Weekly Runrate",
//     subLabel: "(Unit)",
//     sLabel: "NPI Weekly Runrate",
//     value: "npi_weekly_runrate",
//     width: 210,
//     headerSlot: "commonHeader",
//     // children: [
//     //   {
//     //     prop: "npi_weekly_runrate",
//     //     label: "(Unit)",
//     //     width: 210,
//     //   },
//     // ],
//   },
//   {
//     prop: "woi_h",
//     label: "WOI",
//     subLabel: "(Unit)",
//     sLabel: "WOI",
//     value: "woi_h",
//     width: 90,
//     headerSlot: "commonHeader",
//     // children: [
//     //   {
//     //     prop: "woi",
//     //     label: "(Unit)",
//     //     width: 90,
//     //   },
//     // ],
//   },
// ];

export const resultColumns = [
  {
    prop: "pos_authorized_cnt_h",
    label: "Total POS Authorized",
    width: 224,
    sLabel: "Total POS Authorized",
    value: "pos_authorized_cnt_h",
    children: [
      {
        prop: "pos_authorized_cnt",
        label: "(Count)",
        width: 224,
      },
    ],
  },
  {
    prop: "pos_suspension_cnt_h",
    label: "POS on Suspension",
    sLabel: "POS on Suspension",
    width: 208,
    value: "pos_suspension_cnt_h",
    children: [
      {
        prop: "pos_suspension_cnt",
        label: "(Count)",
        width: 208,
      },
    ],
  },
  {
    prop: "supply_unit",
    label: "POS w/ Allocation",
    sLabel: "POS w/ Allocation",
    value: "supply_unit",
    disabled: true,
    width: 192,
    children: [
      {
        prop: "pos_authorized_minus_suspension",
        label: "(Count)",
        width: 106,
      },
      {
        prop: "authorized_pos_suspension_pct",
        label: "(%)",
        width: 86,
      },
    ],
  },
  {
    prop: "allocation_mix_h",
    label: "Allocation Mix - Reward",
    sLabel: "Allocation Mix  - Reward",
    value: "allocation_mix_h",
    width: 240,
    children: [
      {
        prop: "allocation_mix",
        label: "(%)",
        width: 240,
      },
    ],
  },
  {
    prop: "allocation_mix_woi_cap_menu",
    label: "Allocation Mix (Cap by WOI)",
    sLabel: "Allocation Mix (Cap by WOI)",
    disabled: true,
    value: "allocation_mix_woi_cap_menu",
    width: 280,
    children: [
      {
        prop: "allocation_mix_woi_cap",
        label: "(%)",
        width: 130,
      },
      {
        prop: "allocation_mix_woi_cap_vs_reward",
        label: "(Reward)",
        width: 150,
        definedSlot: "capReward",
      },
    ],
  },
  {
    prop: "allocation_mix_ly_h",
    label: "LY Allocation Mix",
    sLabel: "LY Allocation Mix",
    value: "allocation_mix_ly_h",
    width: 182,
    children: [
      {
        prop: "allocation_mix_ly",
        label: "(%)",
        width: 182,
      },
    ],
  },
  {
    prop: "allocation_qty_h",
    label: "Allocation Unit",
    sLabel: "Allocation Unit",
    value: "allocation_qty_h",
    width: 168,
    children: [
      {
        prop: "allocation_qty",
        label: "(Unit, K)",
        width: 168,
      },
    ],
  },
  {
    prop: "allocation_yoy_h",
    label: "Allocation Unit YoY",
    sLabel: "Allocation Unit YoY",
    value: "allocation_yoy_h",
    width: 200,
    children: [
      {
        prop: "allocation_yoy",
        label: "(%)",
        width: 200,
      },
    ],
  },
  {
    prop: "avg_allocation_per_pos_h",
    label: "Avg Allocation per POS",
    sLabel: "Avg Allocation per POS",
    value: "avg_allocation_per_pos_h",
    width: 232,
    children: [
      {
        prop: "avg_allocation_per_pos",
        label: "(Unit)",
        width: 232,
      },
    ],
  },
  {
    prop: "npi_weekly_runrate_h",
    label: "NPI Weekly Runrate",
    sLabel: "NPI Weekly Runrate",
    value: "npi_weekly_runrate_h",
    width: 238,
    headerSlot: "tipsHeader",

    disabled: true,
    tips: "POS (T2) = China NPI Weekly SO * (Q2 + Q3 BAU Mix)<br/> Sold-to = Sum of POS (T2 SO)",
    children: [
      {
        prop: "npi_weekly_runrate",
        label: "(Unit)",
        width: 238,
      },
    ],
  },
  {
    prop: "woi_h",
    label: "WOI",
    sLabel: "WOI",
    value: "woi_h",
    width: 188,
    headerSlot: "tipsHeader",

    disabled: true,
    tips: "WOI =Allocation Unit / NPI weekly Runrate",
    children: [
      {
        prop: "woi",
        label: "(Unit)",
        width: 188,
      },
    ],
  },
];

export const tipsMap = {
  Online: "Channel Online and Carrier Online.",
  Offline: "Monobrand (incl. O2O), Multibrand, Carrier Offline and Education.",
  Vertical: "Partially Enterprise and Education.",
  Strategic: "Please add the list of Strategic Partners.",
};

export const defaultData = [
  {
    supply_from: "China_Total",
    supply_qty: 0,
    supply_unit: 0,
    ly_npi: "0",
    ly_supply: 0,
    npi_weekly_so_forecast: 0,
    bau_mix: 0,
    woi_cap_adjustment: 0.5,
    yoy: 0,
    rtm_mix: 0,
  },
  {
    supply_from: "Online",
    supply_qty: 0,
    supply_unit: 0,
    ly_npi: "0",
    ly_supply: 0,
    npi_weekly_so_forecast: 0,
    bau_mix: 0,
    woi_cap_adjustment: 0.5,
    yoy: 0,
    rtm_mix: 0,
  },
  {
    supply_from: "Offline",
    supply_qty: 0,
    supply_unit: 0,
    ly_npi: "0",
    ly_supply: 0,
    npi_weekly_so_forecast: 0,
    bau_mix: 0,
    woi_cap_adjustment: 0.5,
    yoy: 0,
    rtm_mix: 0,
  },
  {
    supply_from: "Vertical",
    supply_qty: 0,
    supply_unit: 0,
    ly_npi: "0",
    ly_supply: 0,
    npi_weekly_so_forecast: 0,
    bau_mix: 0,
    woi_cap_adjustment: 0.5,
    yoy: 0,
    rtm_mix: 0,
  },
  {
    supply_from: "Strategic",
    supply_qty: 0,
    supply_unit: 0,
    ly_npi: "0",
    ly_supply: 0,
    npi_weekly_so_forecast: 0,
    bau_mix: 0,
    woi_cap_adjustment: 0.5,
    yoy: 0,
    rtm_mix: 0,
  },
];

export const resultSubheader = {
  allocation_mix: "(%)",
  allocation_mix_ly: "(%)",
  allocation_mix_woi_cap: "(%)",
  allocation_mix_woi_cap_vs_reward: "(Reward)",
  allocation_qty: "(Unit, K)",
  allocation_qty_ly: 7171,
  allocation_yoy: "(%)",
  authorized_pos_suspension_pct: "(%)",
  avg_allocation_per_pos: "(Unit)",
  bau_mix: 0.02096369999999999,
  fiscal_year_name: "FY25",
  id: "Consumer_Monobrand_Lifestyle_1453162_11111",
  npi_weekly_runrate: "(Unit)",
  parent_id: "Consumer_Monobrand_Lifestyle_All",
  platform: "Offline",
  pos_authorized_cnt: "(Count)",
  pos_authorized_minus_suspension: "(Count)",
  pos_suspension_cnt: "(Count)",
  rtm: "",
  sold_to_id: "",
  sold_to_name: "",
  sold_to_name_abbre: null,
  sub_rtm: "",
  tier: "",
  tier_allocation_mix: 0.011066200000000002,
  tier_bau_mix: 0.010482199999999997,
  version: "FY25Q3W12",
  woi: "(Unit)",
};

export const Soldto = "Sold-to";
export const BusinessType = "Business Type";
export const SubTypeOptions = [
  { label: BusinessType, value: BusinessType },
  { label: Soldto, value: Soldto },
];

export const supplyFromMap = {
  China_Total: "China Total",
  Online: "Online",
  Offline: "Offline",
  Vertical: "Vertical",
  Strategic: "Program",
};

export const ProTier = "Pro";
export const ConsumerTier = "Consumer";
